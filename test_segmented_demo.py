#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分段搜索演示 - 不依赖网络请求的演示版本
展示长路径分段搜索的核心逻辑
"""

import math
from typing import List, Tuple, Dict, Any


class RouteSegmentDemo:
    """路线分段搜索演示类"""
    
    def __init__(self):
        self.poi_types = {
            "加油站": "010300",
            "餐厅": "050000", 
            "服务区": "180300",
            "充电站": "010301"
        }
    
    def generate_mock_route(self, start_lng: float, start_lat: float, 
                           end_lng: float, end_lat: float, 
                           num_points: int = 100) -> List[Tuple[float, float]]:
        """
        生成模拟路线坐标点
        :param start_lng: 起点经度
        :param start_lat: 起点纬度  
        :param end_lng: 终点经度
        :param end_lat: 终点纬度
        :param num_points: 生成的坐标点数量
        :return: 坐标点列表
        """
        coordinates = []
        
        for i in range(num_points):
            # 线性插值生成路线点
            ratio = i / (num_points - 1)
            lng = start_lng + (end_lng - start_lng) * ratio
            lat = start_lat + (end_lat - start_lat) * ratio
            
            # 添加一些随机偏移模拟真实路线的弯曲
            if i > 0 and i < num_points - 1:
                offset = 0.001 * math.sin(i * 0.5)  # 小幅度偏移
                lng += offset
                lat += offset * 0.5
                
            coordinates.append((lng, lat))
            
        return coordinates
    
    def split_route_into_segments(self, coordinates: List[Tuple[float, float]], 
                                  max_points_per_segment: int = 30) -> List[List[Tuple[float, float]]]:
        """
        将长路线分割成多个段，每段控制在指定点数内
        :param coordinates: 完整路线坐标
        :param max_points_per_segment: 每段最大点数
        :return: 分段后的坐标列表
        """
        if len(coordinates) <= max_points_per_segment:
            return [coordinates]
        
        segments = []
        overlap_points = 3  # 段间重叠点数，确保连续性
        
        start_idx = 0
        while start_idx < len(coordinates):
            # 计算当前段的结束索引
            end_idx = min(start_idx + max_points_per_segment, len(coordinates))
            
            # 提取当前段
            segment = coordinates[start_idx:end_idx]
            segments.append(segment)
            
            # 如果已经到达终点，退出循环
            if end_idx >= len(coordinates):
                break
                
            # 下一段的起始点要往前重叠几个点，确保连续性
            start_idx = end_idx - overlap_points
            
        return segments
    
    def create_buffer_polygon(self, coordinates: List[Tuple[float, float]], 
                             buffer_distance: float = 1000) -> List[Tuple[float, float]]:
        """
        为路线段创建缓冲区多边形
        :param coordinates: 路线坐标点
        :param buffer_distance: 缓冲区距离（米）
        :return: 多边形坐标点
        """
        if len(coordinates) < 2:
            return []
        
        left_points = []  # 路线左侧的点
        right_points = []  # 路线右侧的点
        
        # 为每个路段创建缓冲区
        for i in range(len(coordinates) - 1):
            lng1, lat1 = coordinates[i]
            lng2, lat2 = coordinates[i + 1]
            
            # 计算地理偏移量
            lat_offset = buffer_distance / 111000  # 1度纬度约111km
            lng_offset = buffer_distance / (111000 * math.cos(math.radians((lat1 + lat2) / 2)))
            
            # 计算路段方向向量
            dx = lng2 - lng1
            dy = lat2 - lat1
            length = math.sqrt(dx * dx + dy * dy)
            
            if length > 0:
                # 计算垂直于路段的单位向量
                perp_x = -dy / length * lng_offset
                perp_y = dx / length * lat_offset
                
                # 添加左右两侧的缓冲点
                left_points.append((lng1 + perp_x, lat1 + perp_y))
                right_points.append((lng1 - perp_x, lat1 - perp_y))
        
        # 处理最后一个点
        if coordinates and len(coordinates) >= 2:
            last_lng, last_lat = coordinates[-1]
            prev_lng, prev_lat = coordinates[-2]
            
            dx = last_lng - prev_lng
            dy = last_lat - prev_lat
            length = math.sqrt(dx * dx + dy * dy)
            
            if length > 0:
                lat_offset = buffer_distance / 111000
                lng_offset = buffer_distance / (111000 * math.cos(math.radians(last_lat)))
                perp_x = -dy / length * lng_offset
                perp_y = dx / length * lat_offset
                
                left_points.append((last_lng + perp_x, last_lat + perp_y))
                right_points.append((last_lng - perp_x, last_lat - perp_y))
        
        # 构建完整多边形：左侧点 + 反向右侧点
        polygon = left_points + right_points[::-1]
        
        # 闭合多边形
        if polygon and polygon[0] != polygon[-1]:
            polygon.append(polygon[0])
            
        return polygon
    
    def mock_poi_search(self, polygon: List[Tuple[float, float]], 
                       keywords: str, segment_id: int) -> List[Dict[str, Any]]:
        """
        模拟POI搜索（不实际调用API）
        :param polygon: 搜索多边形
        :param keywords: 搜索关键词
        :param segment_id: 段ID
        :return: 模拟的POI列表
        """
        # 模拟在每个段中找到一些POI
        mock_pois = []
        
        # 根据多边形大小模拟POI数量
        poi_count = min(len(polygon) // 10, 5)  # 每段最多5个POI
        
        for i in range(poi_count):
            # 在多边形内生成模拟POI坐标
            if len(polygon) >= 4:
                # 简单地在多边形中心附近生成点
                center_lng = sum(p[0] for p in polygon) / len(polygon)
                center_lat = sum(p[1] for p in polygon) / len(polygon)
                
                # 添加小幅偏移
                offset_lng = (i - poi_count/2) * 0.001
                offset_lat = (i - poi_count/2) * 0.0005
                
                poi = {
                    'id': f'seg{segment_id}_poi{i+1}',
                    'name': f'{keywords}{i+1}',
                    'address': f'模拟地址{segment_id}-{i+1}',
                    'location': f'{center_lng + offset_lng:.6f},{center_lat + offset_lat:.6f}',
                    'segment': segment_id
                }
                mock_pois.append(poi)
        
        return mock_pois
    
    def demo_segmented_search(self, start_coords: Tuple[float, float], 
                             end_coords: Tuple[float, float],
                             keywords: str = "服务区",
                             route_points: int = 150,
                             max_points_per_segment: int = 25,
                             buffer_distance: float = 1000) -> Dict[str, Any]:
        """
        演示分段搜索的完整流程
        """
        print("🗺️  路线分段搜索演示")
        print("=" * 50)
        
        # 1. 生成模拟路线
        print(f"📍 生成模拟路线: {route_points} 个坐标点")
        coordinates = self.generate_mock_route(
            start_coords[0], start_coords[1],
            end_coords[0], end_coords[1], 
            route_points
        )
        
        # 2. 判断是否需要分段
        if len(coordinates) <= max_points_per_segment * 2:
            print("📏 路线较短，建议使用单段搜索")
            return {"message": "路线较短，无需分段"}
        
        # 3. 分段处理
        print(f"📏 开始分段处理，每段最多 {max_points_per_segment} 个点")
        segments = self.split_route_into_segments(coordinates, max_points_per_segment)
        
        print(f"📏 路线分段完成: {len(coordinates)} 个点 → {len(segments)} 段")
        for i, segment in enumerate(segments):
            print(f"   段 {i+1}: {len(segment)} 个点")
        
        # 4. 逐段搜索
        all_pois = []
        poi_ids_seen = set()
        
        for i, segment_coords in enumerate(segments):
            print(f"\n🔍 正在处理第 {i+1}/{len(segments)} 段...")
            
            # 创建多边形
            polygon = self.create_buffer_polygon(segment_coords, buffer_distance)
            print(f"   创建了包含 {len(polygon)-1} 个顶点的搜索多边形")
            
            # 模拟搜索POI
            segment_pois = self.mock_poi_search(polygon, keywords, i+1)
            
            # 去重处理
            new_pois = []
            for poi in segment_pois:
                poi_id = poi.get('id')
                if poi_id and poi_id not in poi_ids_seen:
                    poi_ids_seen.add(poi_id)
                    new_pois.append(poi)
            
            all_pois.extend(new_pois)
            print(f"   找到 {len(segment_pois)} 个POI，去重后新增 {len(new_pois)} 个")
        
        # 5. 汇总结果
        result = {
            "status": "success",
            "route_info": {
                "total_points": len(coordinates),
                "segments": len(segments),
                "max_points_per_segment": max_points_per_segment
            },
            "search_results": {
                "total_pois": len(all_pois),
                "pois": all_pois
            }
        }
        
        print(f"\n✅ 分段搜索完成！")
        print(f"   总计找到 {len(all_pois)} 个不重复的{keywords}")
        
        return result


def main():
    """演示主函数"""
    demo = RouteSegmentDemo()
    
    # 模拟上海到杭州的长距离路线
    start_coords = (121.604218, 31.245483)  # 上海外滩
    end_coords = (120.108319, 30.24046)     # 杭州西湖
    
    # 演示分段搜索
    result = demo.demo_segmented_search(
        start_coords=start_coords,
        end_coords=end_coords,
        keywords="服务区",
        route_points=150,  # 模拟150个路线点（较长路线）
        max_points_per_segment=25,  # 每段最多25个点
        buffer_distance=1000  # 1公里缓冲区
    )
    
    # 显示详细结果
    if result.get("status") == "success":
        print("\n" + "=" * 50)
        print("📊 搜索结果统计:")
        route_info = result["route_info"]
        search_results = result["search_results"]
        
        print(f"   原始路线点数: {route_info['total_points']}")
        print(f"   分段数量: {route_info['segments']}")
        print(f"   每段最大点数: {route_info['max_points_per_segment']}")
        print(f"   找到POI总数: {search_results['total_pois']}")
        
        # 显示前几个POI
        pois = search_results["pois"]
        if pois:
            print(f"\n📍 POI详情:")
            for poi in pois[:8]:  # 显示前8个
                print(f"   • {poi['name']} (第{poi['segment']}段)")
                print(f"     地址: {poi['address']}")
                print(f"     坐标: {poi['location']}")
    
    print("\n" + "=" * 50)
    print("💡 分段搜索的优势:")
    print("   ✓ 避免URL长度限制")
    print("   ✓ 提高搜索成功率") 
    print("   ✓ 更好的错误恢复能力")
    print("   ✓ 可以并行处理各段")
    print("   ✓ 内存使用更高效")


if __name__ == "__main__":
    main()
